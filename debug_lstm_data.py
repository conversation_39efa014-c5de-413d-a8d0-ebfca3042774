#!/usr/bin/env python
"""
Debug LSTM Data Issues

This script investigates why LSTM is performing so poorly even with correct configuration.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_data():
    """Debug the data loading and preprocessing."""
    
    # Load data
    file_path = Path('data/historical/btcusd.a/BTCUSD.a_M5.parquet')
    logger.info(f"Loading data from: {file_path}")
    
    try:
        df = pd.read_parquet(file_path)
        logger.info(f"Data loaded successfully: {df.shape}")
        
        # Check columns
        logger.info(f"Columns: {list(df.columns)}")
        logger.info(f"Data types:\n{df.dtypes}")
        
        # Check for required columns
        required_cols = ['open', 'high', 'low', 'close', 'real_volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"Missing required columns: {missing_cols}")
        else:
            logger.info("✅ All required columns present")
        
        # Check data quality
        logger.info("\nData quality check:")
        for col in required_cols:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                inf_count = np.isinf(df[col]).sum()
                logger.info(f"{col}: {null_count} nulls, {inf_count} infinites")
        
        # Show sample data
        logger.info(f"\nFirst 5 rows:")
        logger.info(df[required_cols].head())
        
        logger.info(f"\nLast 5 rows:")
        logger.info(df[required_cols].tail())
        
        # Check data ranges
        logger.info(f"\nData statistics:")
        logger.info(df[required_cols].describe())
        
        # Check if data is reasonable
        close_prices = df['close']
        logger.info(f"\nClose price range: {close_prices.min():.2f} to {close_prices.max():.2f}")
        
        # Check for extreme values
        for col in required_cols:
            if col in df.columns:
                q99 = df[col].quantile(0.99)
                q01 = df[col].quantile(0.01)
                extreme_high = (df[col] > q99 * 10).sum()
                extreme_low = (df[col] < q01 / 10).sum()
                if extreme_high > 0 or extreme_low > 0:
                    logger.warning(f"{col}: {extreme_high} extreme high, {extreme_low} extreme low values")
        
        # Test preprocessing
        logger.info("\nTesting preprocessing...")
        feature_columns = ['open', 'high', 'low', 'close', 'real_volume']
        df_clean = df[feature_columns + ['close']].copy()
        df_clean = df_clean.dropna()
        
        logger.info(f"Clean data shape: {df_clean.shape}")
        
        # Test scaling
        from sklearn.preprocessing import StandardScaler
        
        X = df_clean[feature_columns].values
        y = df_clean['close'].values.reshape(-1, 1)
        
        logger.info(f"X shape: {X.shape}, y shape: {y.shape}")
        
        X_scaler = StandardScaler()
        y_scaler = StandardScaler()
        
        X_scaled = X_scaler.fit_transform(X)
        y_scaled = y_scaler.fit_transform(y)
        
        logger.info(f"X_scaled stats: mean={X_scaled.mean():.6f}, std={X_scaled.std():.6f}")
        logger.info(f"y_scaled stats: mean={y_scaled.mean():.6f}, std={y_scaled.std():.6f}")
        
        # Check for issues in scaled data
        if np.isnan(X_scaled).any():
            logger.error("❌ NaN values in scaled X")
        if np.isnan(y_scaled).any():
            logger.error("❌ NaN values in scaled y")
        if np.isinf(X_scaled).any():
            logger.error("❌ Infinite values in scaled X")
        if np.isinf(y_scaled).any():
            logger.error("❌ Infinite values in scaled y")
        
        # Test sequence creation
        sequence_length = 60
        X_sequences = []
        y_sequences = []
        
        for i in range(min(1000, len(X_scaled) - sequence_length)):  # Test first 1000
            X_sequences.append(X_scaled[i:i+sequence_length])
            y_sequences.append(y_scaled[i+sequence_length])
        
        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)
        
        logger.info(f"Sequence shapes: X={X_sequences.shape}, y={y_sequences.shape}")
        
        # Check sequence data quality
        if np.isnan(X_sequences).any():
            logger.error("❌ NaN values in X sequences")
        if np.isnan(y_sequences).any():
            logger.error("❌ NaN values in y sequences")
        
        logger.info("✅ Data debugging completed")
        
        return df
        
    except Exception as e:
        logger.error(f"Error loading data: {str(e)}")
        return None

def debug_model_comparison():
    """Compare with successful ARIMA approach."""
    logger.info("\n" + "="*50)
    logger.info("COMPARING WITH SUCCESSFUL ARIMA APPROACH")
    logger.info("="*50)
    
    # Check ARIMA metrics
    arima_file = Path('metrics/arima_BTCUSD.a_M5_20250604_125107.json')
    if arima_file.exists():
        import json
        with open(arima_file, 'r') as f:
            arima_metrics = json.load(f)
        
        logger.info("ARIMA (successful) configuration:")
        logger.info(f"- R²: {arima_metrics.get('r2', 'N/A')}")
        logger.info(f"- Features: 85+ engineered features")
        logger.info(f"- Approach: Ensemble with multiple models")
        
        logger.info("\nLSTM (failing) configuration:")
        logger.info("- R²: -3.34 (terrible)")
        logger.info("- Features: 5 basic features")
        logger.info("- Approach: Single model")
        
        logger.info("\nKey differences:")
        logger.info("1. ARIMA uses 85+ engineered features vs LSTM 5 basic")
        logger.info("2. ARIMA uses ensemble vs LSTM single model")
        logger.info("3. Different preprocessing approaches")

def main():
    """Main debugging function."""
    logger.info("🔍 DEBUGGING LSTM PERFORMANCE ISSUES")
    logger.info("="*60)
    
    # Debug data
    df = debug_data()
    
    if df is not None:
        # Compare approaches
        debug_model_comparison()
        
        logger.info("\n🎯 POTENTIAL ISSUES IDENTIFIED:")
        logger.info("1. Data scaling might be inappropriate for LSTM")
        logger.info("2. LSTM might need different preprocessing than ARIMA")
        logger.info("3. Model architecture might need adjustment")
        logger.info("4. Training parameters might be suboptimal")
        
        logger.info("\n💡 NEXT STEPS:")
        logger.info("1. Try different scaling approaches")
        logger.info("2. Investigate successful LSTM implementations")
        logger.info("3. Consider hybrid approach")
        logger.info("4. Validate against known working examples")

if __name__ == "__main__":
    main()
