#!/usr/bin/env python
"""
Test Improved LSTM Model with Advanced Feature Engineering

This script tests the improved LSTM model with 85+ features on M5 timeframe only
to validate the fixes before running full training.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/test_lstm_improved.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import the improved training function
from train_lstm_btcusd import train_lstm_model

def main():
    """Test the improved LSTM model on M5 timeframe."""
    logger.info("="*60)
    logger.info("TESTING IMPROVED LSTM MODEL WITH ADVANCED FEATURES")
    logger.info("="*60)
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    # Test on M5 timeframe only
    timeframe = 'M5'
    
    logger.info(f"Testing improved LSTM model on {timeframe} timeframe...")
    logger.info("Key improvements:")
    logger.info("- Advanced feature engineering (85+ features)")
    logger.info("- Increased model capacity (128 hidden units, 3 layers)")
    logger.info("- Optimized hyperparameters")
    logger.info("- Better regularization (30% dropout)")
    
    try:
        result = train_lstm_model(
            timeframe=timeframe,
            feature_columns=None,  # Will use all engineered features
            target_column='close',
            sequence_length=60,
            hidden_units=256,
            num_layers=4,
            dropout_rate=0.4,
            learning_rate=0.0001,
            epochs=100,  # Increased for better training
            batch_size=128,
            validation_split=0.1,
            test_size=0.2,
            random_state=42,
            use_gpu=True
        )
        
        if result:
            metrics = result['metrics']
            logger.info("="*60)
            logger.info("IMPROVED LSTM TEST RESULTS")
            logger.info("="*60)
            logger.info(f"Timeframe: {timeframe}")
            logger.info(f"MSE: {metrics['mse']:.2f}")
            logger.info(f"RMSE: {metrics['rmse']:.2f}")
            logger.info(f"MAE: {metrics['mae']:.2f}")
            logger.info(f"R²: {metrics['r2']:.6f}")
            
            # Performance evaluation
            r2 = metrics['r2']
            if r2 > 0.95:
                logger.info("🎉 EXCELLENT: R² > 0.95 - Model performing very well!")
                status = "EXCELLENT"
            elif r2 > 0.80:
                logger.info("✅ GOOD: R² > 0.80 - Significant improvement!")
                status = "GOOD"
            elif r2 > 0.50:
                logger.info("⚠️ MODERATE: R² > 0.50 - Some improvement")
                status = "MODERATE"
            elif r2 > 0.0:
                logger.info("❌ POOR: R² > 0.0 - Still underperforming")
                status = "POOR"
            else:
                logger.info("🚨 CRITICAL: R² < 0.0 - Model worse than baseline")
                status = "CRITICAL"
            
            # Compare with previous performance
            previous_r2 = -4.875  # From the terrible result
            improvement = r2 - previous_r2
            
            logger.info("="*60)
            logger.info("PERFORMANCE COMPARISON")
            logger.info("="*60)
            logger.info(f"Previous R²: {previous_r2:.3f}")
            logger.info(f"Current R²:  {r2:.6f}")
            logger.info(f"Improvement: +{improvement:.3f}")
            logger.info(f"Status: {status}")
            
            if r2 > 0.90:
                logger.info("✅ SUCCESS: Ready for full training on all timeframes!")
                return True
            elif r2 > 0.50:
                logger.info("⚠️ PARTIAL SUCCESS: Significant improvement but may need more tuning")
                return True
            else:
                logger.info("❌ NEEDS MORE WORK: Additional improvements required")
                return False
                
        else:
            logger.error("❌ TRAINING FAILED: Could not train improved LSTM model")
            return False
            
    except Exception as e:
        logger.error(f"❌ ERROR during training: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("\n🎯 RECOMMENDATION: Proceed with full LSTM training using improved configuration")
        sys.exit(0)
    else:
        logger.info("\n🔧 RECOMMENDATION: Review and adjust LSTM configuration before full training")
        sys.exit(1)
