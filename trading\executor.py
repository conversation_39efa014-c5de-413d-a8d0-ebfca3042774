"""
Trade execution module for executing trades through MT5.
"""
import logging
from typing import Dict, List, Optional, Any
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import time
import threading

from utils.mt5.mt5_connection_manager import MT5ConnectionManager
from utils.error_handler import <PERSON>rror<PERSON>andler
from config.unified_config import TradingConfig

logger = logging.getLogger(__name__)

class TradeExecutor:
    """
    Trade executor class for executing trades through MT5.

    Handles order placement, order modification, and order closure.
    """

    def __init__(self,
                 mt5_manager: MT5ConnectionManager,
                 terminal_id: int,
                 config: TradingConfig,
                 error_handler: Optional[ErrorHandler] = None):
        """
        Initialize the trade executor.

        Args:
            mt5_manager: MT5 connection manager
            terminal_id: MT5 terminal ID
            config: Trading configuration
            error_handler: Error handler
        """
        self.mt5_manager = mt5_manager
        self.terminal_id = terminal_id
        self.config = config
        self.error_handler = error_handler
        self._lock = threading.RLock()
        self.active_orders = {}
        self.profit_loss = 0.0
        self.trade_count = 0
        self.winning_trades = 0
        self.losing_trades = 0
        # Check if config has a strategy attribute or is itself a strategy config
        if hasattr(config, 'strategy'):
            self.symbol = config.strategy.symbol
            self.max_positions = config.strategy.max_positions
        else:
            # Assume config is the strategy config itself
            self.symbol = config.symbol if hasattr(config, 'symbol') else "BTCUSD.a"
            self.max_positions = config.max_positions if hasattr(config, 'max_positions') else 3

        # BTCUSD.a specific parameters
        self.point_value = 0.1  # BTCUSD.a point value
        self.min_lot_size = 0.01  # Minimum lot size for BTCUSD.a
        self.max_lot_size = 0.5  # Maximum lot size for BTCUSD.a
        self.leverage = 1  # BTCUSD.a leverage
        self.max_slippage_pips = 100  # Increased for BTCUSD.a volatility
        self.execution_timeout = 30  # Increased timeout for crypto execution
        self.max_spread_pips = 5000  # Maximum spread in pips for BTCUSD.a (crypto has high spreads)
        self.stop_loss_pips = 50  # Default stop loss in pips
        self.take_profit_pips = 100  # Default take profit in pips

        # Initialize circuit breaker (simple implementation)
        self.order_circuit = self._create_simple_circuit_breaker()

        logger.info(f"TradeExecutor initialized for terminal {terminal_id} trading BTCUSD.a")

    def execute_trade(self, signal) -> Dict[str, Any]:
        """
        Execute a trade based on the provided signal.
        Adjusted for BTCUSD.a execution characteristics.

        Args:
            signal: Trading signal (TradingSignal object or dict) with action, confidence, etc.

        Returns:
            Dict[str, Any]: Trade execution result
        """
        try:
            # Extract signal information - handle both dict and TradingSignal object
            if hasattr(signal, 'action'):
                # TradingSignal object
                action = getattr(signal, 'action', 'HOLD')
                confidence = getattr(signal, 'confidence', 0.0)
                volume = getattr(signal, 'volume', None)
            else:
                # Dictionary
                action = signal.get('action', 'HOLD')
                confidence = signal.get('confidence', 0.0)
                volume = signal.get('volume', None)

            # Skip if action is HOLD
            if action == 'HOLD':
                logger.info("Signal action is HOLD, no trade to execute")
                return {
                    'success': True,
                    'message': 'No trade executed (HOLD signal)',
                    'ticket': None,
                    'profit_loss': 0.0
                }

            # Get account info
            account_info = self.get_account_info()

            # Check if we have enough free margin
            if hasattr(account_info, 'margin_free') and account_info.margin_free <= 0:
                logger.warning("Not enough free margin to execute trade")
                return {
                    'success': False,
                    'message': 'Not enough free margin',
                    'ticket': None,
                    'profit_loss': 0.0
                }

            # Get current price
            price_info = self.get_current_price(self.symbol)

            # Check spread (adjusted for BTCUSD.a)
            current_spread = price_info.get('spread', 0.0)
            if current_spread > self.max_spread_pips:
                logger.warning(f"Spread too high: {current_spread} pips > {self.max_spread_pips} pips")
                return {
                    'success': False,
                    'message': f'Spread too high: {current_spread} pips',
                    'ticket': None,
                    'profit_loss': 0.0
                }

            # Calculate position size (adjusted for BTCUSD.a)
            if volume is None:
                volume = self.calculate_position_size(account_info)

            # Ensure volume is within BTCUSD.a limits
            volume = max(self.min_lot_size, min(volume, self.max_lot_size))

            # Set up trade parameters
            trade_params = {
                'action': mt5.TRADE_ACTION_DEAL,
                'symbol': self.symbol,
                'volume': volume,
                'type': mt5.ORDER_TYPE_BUY if action == 'BUY' else mt5.ORDER_TYPE_SELL,
                'price': price_info.get('ask' if action == 'BUY' else 'bid', 0.0),
                'deviation': self.max_slippage_pips,
                'magic': 123456,  # Magic number for identifying bot trades
                'comment': f"Bot trade - {confidence:.2f} confidence",
                'type_time': mt5.ORDER_TIME_GTC,
                'type_filling': mt5.ORDER_FILLING_IOC,
            }

            # Add SL/TP if configured
            point_value = self.point_value

            if self.stop_loss_pips > 0:
                if action == 'BUY':
                    trade_params['sl'] = trade_params['price'] - (self.stop_loss_pips * point_value)
                else:
                    trade_params['sl'] = trade_params['price'] + (self.stop_loss_pips * point_value)

            if self.take_profit_pips > 0:
                if action == 'BUY':
                    trade_params['tp'] = trade_params['price'] + (self.take_profit_pips * point_value)
                else:
                    trade_params['tp'] = trade_params['price'] - (self.take_profit_pips * point_value)

            # Execute the trade with circuit breaker
            def _execute_order():
                # Ensure connection is established
                connection = self.mt5_manager.get_connection(self.terminal_id)
                if not connection or not connection.is_connected:
                    raise RuntimeError(f"MT5 connection not available for terminal {self.terminal_id}")

                # Send trade request
                result = mt5.order_send(trade_params)

                if result is None:
                    error_code = mt5.last_error()
                    raise RuntimeError(f"Failed to send order: MT5 error code {error_code}")

                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    raise RuntimeError(f"Order failed: {result.retcode} - {result.comment}")

                logger.info(f"Trade executed: {action} {volume} lots of {self.symbol} at {trade_params['price']}")

                return {
                    'success': True,
                    'message': f"{action} order executed",
                    'ticket': result.order,
                    'volume': volume,
                    'price': trade_params['price'],
                    'profit_loss': 0.0  # Profit/loss will be updated when position is closed
                }

            # Execute with circuit breaker
            return self.order_circuit.execute(
                _execute_order,
                circuit_name=f"order_execution_{self.terminal_id}"
            )

        except Exception as e:
            error_info = self.error_handler.handle_error(e, context={
                'component': 'trade_execution',
                'method': 'execute_trade',
                'signal': signal
            })

            logger.error(f"Trade execution failed: {error_info.error_message}")

            return {
                'success': False,
                'message': f"Trade failed: {str(e)}",
                'ticket': None,
                'profit_loss': 0.0
            }

    def close_trade(self, ticket: int) -> bool:
        """
        Close a specific trade.

        Args:
            ticket: Order ticket number

        Returns:
            bool: True if closed successfully, False otherwise
        """
        with self._lock:
            try:
                # Ensure connection is active
                connection = self.mt5_manager.get_connection(self.terminal_id)
                if not connection or not connection.is_connected:
                    logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                    return False

                # Get position info
                position = mt5.positions_get(ticket=ticket)
                if position is None or len(position) == 0:
                    logger.error(f"Position with ticket {ticket} not found")
                    return False

                position = position[0]

                # Determine position direction
                order_type = mt5.ORDER_TYPE_SELL if position.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY

                # Create close request
                request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": position.symbol,
                    "volume": position.volume,
                    "type": order_type,
                    "position": ticket,
                    "price": 0.0,  # Market price
                    "deviation": 10,
                    "magic": 123456,
                    "comment": "Close position",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_FOK
                }

                # Send order
                result = mt5.order_send(request)

                if result is None:
                    error = mt5.last_error()
                    logger.error(f"Close order failed: {error}")
                    return False

                if result.retcode != mt5.TRADE_RETCODE_DONE:
                    logger.error(f"Close order failed with code {result.retcode}: {result.comment}")
                    return False

                # Calculate profit/loss
                if ticket in self.active_orders:
                    entry_price = self.active_orders[ticket]["price"]
                    exit_price = result.price
                    volume = self.active_orders[ticket]["volume"]
                    action = self.active_orders[ticket]["action"]

                    if action == "buy":
                        profit = (exit_price - entry_price) * volume
                    else:
                        profit = (entry_price - exit_price) * volume

                    # Update profit/loss
                    self.profit_loss += profit

                    # Update win/loss count
                    if profit > 0:
                        self.winning_trades += 1
                    else:
                        self.losing_trades += 1

                    # Remove from active orders
                    del self.active_orders[ticket]

                logger.info(f"Position {ticket} closed successfully")
                return True

            except Exception as e:
                if self.error_handler:
                    self.error_handler.handle_error(
                        e,
                        context={
                            "method": "close_trade",
                            "ticket": ticket
                        }
                    )
                logger.error(f"Error closing trade: {str(e)}")
                return False

    def close_all_trades(self) -> bool:
        """
        Close all open positions.

        Returns:
            bool: True if all positions closed successfully, False otherwise
        """
        with self._lock:
            try:
                # Get all open positions
                positions = self.get_open_positions()
                if positions is None:
                    logger.error("Failed to get open positions")
                    return False

                # Close each position
                success = True
                for position in positions:
                    ticket = position.ticket
                    if not self.close_trade(ticket):
                        logger.error(f"Failed to close position {ticket}")
                        success = False

                return success

            except Exception as e:
                if self.error_handler:
                    self.error_handler.handle_error(
                        e,
                        context={"method": "close_all_trades"}
                    )
                logger.error(f"Error closing all trades: {str(e)}")
                return False

    def get_open_positions(self) -> Optional[List]:
        """
        Get all open positions.

        Returns:
            Optional[List]: List of open positions or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Get positions for our symbol
            positions = mt5.positions_get(symbol=self.symbol)
            if positions is None:
                error = mt5.last_error()
                logger.error(f"Failed to get positions: {error}")
                return None

            return positions

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "get_open_positions"}
                )
            logger.error(f"Error getting open positions: {str(e)}")
            return None

    def get_current_price(self, symbol: str) -> Optional[Dict[str, float]]:
        """
        Get current price information for a symbol.

        Args:
            symbol: Symbol to get price for

        Returns:
            Optional[Dict[str, float]]: Price info dict with 'bid', 'ask', 'spread' or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Get symbol information
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                error = mt5.last_error()
                logger.error(f"Failed to get symbol info: {error}")
                return None

            # Calculate spread in points
            spread_points = (symbol_info.ask - symbol_info.bid) / symbol_info.point

            # Return price information as dictionary
            return {
                'bid': symbol_info.bid,
                'ask': symbol_info.ask,
                'spread': spread_points,
                'point': symbol_info.point
            }

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={
                        "method": "get_current_price",
                        "symbol": symbol
                    }
                )
            logger.error(f"Error getting current price: {str(e)}")
            return None

    def _calculate_lot_size(self, confidence: float) -> float:
        """
        Calculate lot size based on confidence and risk management settings.

        Args:
            confidence: Signal confidence (0.0-1.0)

        Returns:
            float: Lot size to trade
        """
        try:
            # Get account info
            account_info = self.get_account_info()
            if account_info is None:
                logger.error("Failed to get account info for lot calculation")
                # Check if config has a strategy attribute or is itself a strategy config
                if hasattr(self.config, 'strategy'):
                    return self.config.strategy.lot_size
                else:
                    return getattr(self.config, 'lot_size', 0.01)

            balance = account_info.balance

            # Calculate risk-adjusted lot size
            if hasattr(self.config, 'strategy'):
                risk = self.config.strategy.risk_per_trade * confidence
            else:
                risk = getattr(self.config, 'risk_per_trade', 0.02) * confidence
            risk_amount = balance * risk

            # Get symbol information for margin calculation
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.warning("Failed to get symbol info for lot calculation")
                return self.config.strategy.lot_size

            contract_size = symbol_info.trade_contract_size

            # Simple lot calculation
            # More sophisticated calculation would consider leverage, margin, etc.
            lot_size = risk_amount / contract_size / 100

            # Ensure minimum and maximum lot size constraints
            min_lot = symbol_info.volume_min
            if hasattr(self.config, 'strategy'):
                max_lot = min(symbol_info.volume_max, self.config.strategy.lot_size * 2)
            else:
                max_lot = min(symbol_info.volume_max, getattr(self.config, 'lot_size', 0.01) * 2)

            lot_size = max(min_lot, min(lot_size, max_lot))

            # Round to symbol lot step
            lot_step = symbol_info.volume_step
            lot_size = round(lot_size / lot_step) * lot_step

            logger.info(f"Calculated lot size: {lot_size} (confidence: {confidence}, risk: {risk:.2%})")
            return lot_size

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "_calculate_lot_size"}
                )
            logger.error(f"Error calculating lot size: {str(e)}")
            if hasattr(self.config, 'strategy'):
                return self.config.strategy.lot_size
            else:
                return getattr(self.config, 'lot_size', 0.01)

    def _get_pip_value(self, symbol: str) -> Optional[float]:
        """
        Get the pip value for a symbol.

        Args:
            symbol: Symbol to get pip value for

        Returns:
            Optional[float]: Pip value or None if failed
        """
        try:
            # Get symbol information
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Failed to get symbol info for {symbol}")
                return None

            # Calculate pip value based on digits
            digits = symbol_info.digits

            # For Forex: 4 digits = 0.0001, 5 digits = 0.00001, etc.
            if digits == 4 or digits == 5:
                pip_value = 10 ** -4
            # For indices, commodities, crypto: varies
            else:
                pip_value = 10 ** -digits

            return pip_value

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={
                        "method": "_get_pip_value",
                        "symbol": symbol
                    }
                )
            logger.error(f"Error getting pip value: {str(e)}")
            return None

    def get_account_info(self) -> Optional[Any]:
        """
        Get account information.

        Returns:
            Optional[Any]: Account information or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Get account info
            account_info = mt5.account_info()
            if account_info is None:
                error = mt5.last_error()
                logger.error(f"Failed to get account info: {error}")
                return None

            return account_info

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "get_account_info"}
                )
            logger.error(f"Error getting account info: {str(e)}")
            return None

    def get_trade_history(self, days: int = 7) -> Optional[List]:
        """
        Get trade history for the specified period.

        Args:
            days: Number of days to look back

        Returns:
            Optional[List]: List of completed trades or None if failed
        """
        try:
            # Ensure connection is active
            connection = self.mt5_manager.get_connection(self.terminal_id)
            if not connection or not connection.is_connected:
                logger.error(f"MT5 connection not available for terminal {self.terminal_id}")
                return None

            # Calculate start time
            from_date = datetime.now() - timedelta(days=days)

            # Get history orders
            history_orders = mt5.history_orders_get(
                symbol=self.symbol,
                from_date=from_date
            )

            if history_orders is None:
                error = mt5.last_error()
                logger.error(f"Failed to get history orders: {error}")
                return None

            # Get history deals
            history_deals = mt5.history_deals_get(
                symbol=self.symbol,
                from_date=from_date
            )

            if history_deals is None:
                error = mt5.last_error()
                logger.error(f"Failed to get history deals: {error}")
                return None

            return {"orders": history_orders, "deals": history_deals}

        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(
                    e,
                    context={"method": "get_trade_history"}
                )
            logger.error(f"Error getting trade history: {str(e)}")
            return None

    def calculate_position_size(self, account_info) -> float:
        """
        Calculate position size based on account balance and risk management.

        Args:
            account_info: MT5 account information

        Returns:
            float: Position size in lots
        """
        try:
            # Get account balance
            balance = getattr(account_info, 'balance', 10000.0)

            # Calculate risk amount (2% of balance by default)
            risk_percent = 0.02
            risk_amount = balance * risk_percent

            # Get symbol info for contract size
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                logger.warning("Failed to get symbol info, using default lot size")
                return self.min_lot_size

            # Simple position sizing: risk amount / (stop loss in currency units)
            # For BTCUSD.a, use a simple percentage of balance
            position_size = risk_amount / (balance * 0.1)  # 10% of risk amount

            # Ensure within limits
            position_size = max(self.min_lot_size, min(position_size, self.max_lot_size))

            # Round to symbol lot step
            lot_step = getattr(symbol_info, 'volume_step', 0.01)
            position_size = round(position_size / lot_step) * lot_step

            logger.info(f"Calculated position size: {position_size} lots")
            return position_size

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return self.min_lot_size

    def _create_simple_circuit_breaker(self):
        """
        Create a simple circuit breaker implementation.

        Returns:
            Simple circuit breaker object
        """
        class SimpleCircuitBreaker:
            def __init__(self):
                self.failure_count = 0
                self.max_failures = 3
                self.reset_timeout = 60
                self.last_failure_time = 0

            def execute(self, func, circuit_name=None):
                """Execute function with circuit breaker protection."""
                try:
                    # Check if circuit is open
                    current_time = time.time()
                    if (self.failure_count >= self.max_failures and
                        current_time - self.last_failure_time < self.reset_timeout):
                        raise RuntimeError("Circuit breaker is open")

                    # Reset if timeout passed
                    if current_time - self.last_failure_time >= self.reset_timeout:
                        self.failure_count = 0

                    # Execute function
                    result = func()

                    # Reset on success
                    self.failure_count = 0
                    return result

                except Exception as e:
                    # Increment failure count
                    self.failure_count += 1
                    self.last_failure_time = time.time()
                    raise e

        return SimpleCircuitBreaker()