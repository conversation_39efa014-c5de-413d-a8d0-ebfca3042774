#!/usr/bin/env python
"""
Best LSTM Training Script - Based on Working R² = 0.882 Configuration

This script uses the proven working configuration and makes minimal targeted improvements:
- Uses the exact configuration that achieved R² = 0.882
- Only adds longer training (more epochs)
- No learning rate scheduling or gradient clipping
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from pathlib import Path
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/train_lstm_best.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import required modules
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

def load_data(timeframe, data_dir='data/historical/btcusd.a'):
    """Load data for a specific timeframe."""
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def preprocess_data_best(df, sequence_length=60, target_column='close', test_size=0.2):
    """
    Best preprocessing - EXACT copy of the working R² = 0.882 configuration.
    """
    logger.info("Using BEST LSTM preprocessing - proven working configuration")
    
    # Use ONLY the 5 basic features that achieved R² = 0.882
    feature_columns = ['open', 'high', 'low', 'close', 'real_volume']
    
    # Validate columns exist
    missing_cols = [col for col in feature_columns + [target_column] if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing columns: {missing_cols}")
    
    # Extract only the required columns - CRITICAL FIX
    df_clean = df[feature_columns].copy()  # Don't include target in features!
    target_data = df[target_column].copy()
    
    # Remove NaN values
    initial_rows = len(df_clean)
    df_clean = df_clean.dropna()
    target_data = target_data.loc[df_clean.index]  # Align target with features
    final_rows = len(df_clean)
    
    logger.info(f"Using {len(feature_columns)} basic features: {feature_columns}")
    logger.info(f"Removed {initial_rows - final_rows} rows with NaN values")
    logger.info(f"Final dataset: {final_rows} rows")
    
    # Extract features and target - CRITICAL FIX
    X = df_clean.values  # Only features, no target
    y = target_data.values.reshape(-1, 1)  # Separate target
    
    # Verify shapes
    logger.info(f"BEFORE SCALING - X shape: {X.shape}, y shape: {y.shape}")
    if X.shape[0] != y.shape[0]:
        raise ValueError(f"Shape mismatch: X has {X.shape[0]} rows, y has {y.shape[0]} rows")
    
    # Scale features and target separately (CRITICAL for LSTM)
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()
    
    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    logger.info(f"AFTER SCALING - X_scaled shape: {X_scaled.shape}, y_scaled shape: {y_scaled.shape}")

    # Create sequences
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    logger.info(f"Created {len(X_sequences)} sequences")
    logger.info(f"FINAL SHAPES - X_sequences: {X_sequences.shape}, y_sequences: {y_sequences.shape}")

    # Verify we have exactly 5 features
    if X_sequences.shape[2] != 5:
        logger.error(f"❌ WRONG FEATURE COUNT: Expected 5, got {X_sequences.shape[2]}")
        raise ValueError(f"Expected 5 features, got {X_sequences.shape[2]}")
    else:
        logger.info(f"✅ CORRECT: Using exactly 5 features as documented")
    
    # Temporal split (CRITICAL - no shuffling for time series)
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]

    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def train_best_lstm(timeframe='M5'):
    """Train LSTM using the proven working configuration with minimal improvements."""
    logger.info("="*60)
    logger.info("TRAINING BEST LSTM MODEL - PROVEN WORKING CONFIGURATION")
    logger.info("="*60)
    logger.info("Configuration (PROVEN to achieve R² = 0.882):")
    logger.info("- Features: 5 basic [open, high, low, close, real_volume]")
    logger.info("- Hidden units: 64")
    logger.info("- Layers: 2")
    logger.info("- Dropout: 0.2")
    logger.info("- Learning rate: 0.001")
    logger.info("- Batch size: 32")
    logger.info("- Sequence length: 60")
    logger.info("- Only improvement: More epochs (150 vs 100)")
    
    # Configure GPU
    gpu_info = get_gpu_info()
    if gpu_info['gpu_available']:
        configure_gpu_memory()
        device = select_device(use_gpu=True)
        logger.info(f"Using device: {device}")
    else:
        device = torch.device('cpu')
        logger.info("Using CPU")
    
    # Load data
    df = load_data(timeframe)
    if df is None:
        logger.error(f"Failed to load data for {timeframe}")
        return None
    
    # Preprocess data with PROVEN approach
    X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data_best(df)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test)
    
    # Create data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Split for validation
    train_size = int(0.9 * len(train_dataset))
    val_size = len(train_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size], generator=torch.Generator().manual_seed(42)
    )
    
    # EXACT batch size from working configuration
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32)
    test_loader = DataLoader(test_dataset, batch_size=32)
    
    # Create model with EXACT working architecture
    _, timesteps, features = X_train.shape
    model = LSTMModel(
        input_dim=features,      # Should be 5
        hidden_dim=64,           # EXACT from working config
        num_layers=2,            # EXACT from working config
        output_dim=1,
        dropout_rate=0.2         # EXACT from working config
    )
    
    logger.info(f"Model created: {features} features -> {64} hidden units -> 1 output")
    
    # Create trainer with EXACT working parameters (NO MODIFICATIONS)
    trainer = LSTMTrainer(
        model=model,
        learning_rate=0.001,     # EXACT from working config
        device=device
    )
    
    # Train model with ONLY increased epochs
    logger.info("Training LSTM with proven working configuration...")
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=150,              # ONLY change: 150 vs 100
        patience=15,             # Slightly more patience
        verbose=True
    )
    
    # Evaluate
    test_loss = trainer.evaluate(test_loader)
    y_pred = trainer.predict(test_loader)
    
    # Inverse transform predictions
    y_pred_inv = y_scaler.inverse_transform(y_pred)
    y_test_inv = y_scaler.inverse_transform(y_test)
    
    # Calculate metrics
    mse = np.mean((y_pred_inv - y_test_inv) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_pred_inv - y_test_inv))
    
    y_mean = np.mean(y_test_inv)
    ss_total = np.sum((y_test_inv - y_mean) ** 2)
    ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
    r2 = 1 - (ss_residual / ss_total)
    
    logger.info("="*60)
    logger.info("BEST LSTM RESULTS")
    logger.info("="*60)
    logger.info(f"MSE: {mse:.2f}")
    logger.info(f"RMSE: {rmse:.2f}")
    logger.info(f"MAE: {mae:.2f}")
    logger.info(f"R²: {r2:.6f}")
    
    # Performance evaluation against documented target
    target_r2 = 0.9997
    previous_r2 = 0.882
    
    if r2 > 0.999:
        logger.info("🎉 EXCELLENT: R² > 0.999 - TARGET ACHIEVED!")
        status = "EXCELLENT"
    elif r2 > 0.99:
        logger.info("✅ VERY GOOD: R² > 0.99 - Very close to target")
        status = "VERY_GOOD"
    elif r2 > 0.95:
        logger.info("✅ GOOD: R² > 0.95 - Good performance")
        status = "GOOD"
    elif r2 > 0.90:
        logger.info("⚠️ MODERATE: R² > 0.90 - Moderate performance")
        status = "MODERATE"
    elif r2 > previous_r2:
        logger.info(f"✅ IMPROVED: R² > {previous_r2} - Better than previous")
        status = "IMPROVED"
    else:
        logger.info("❌ REGRESSION: Performance worse than previous")
        status = "REGRESSION"
    
    # Compare with previous results
    improvement = r2 - previous_r2
    logger.info("="*60)
    logger.info("PERFORMANCE COMPARISON")
    logger.info("="*60)
    logger.info(f"Target R² (documented): {target_r2:.4f}")
    logger.info(f"Current R²:             {r2:.6f}")
    logger.info(f"Previous best R²:       {previous_r2:.6f}")
    logger.info(f"Improvement:            {improvement:+.6f}")
    logger.info(f"Gap to target:          {target_r2 - r2:.6f}")
    logger.info(f"Status: {status}")
    
    # Save model if performance is good
    if r2 > 0.85:
        model_dir = Path(f'models/lstm_BTCUSD.a_{timeframe}_best')
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Save model
        torch.save(model.state_dict(), model_dir / 'model.pt')
        torch.save({'X_scaler': X_scaler, 'y_scaler': y_scaler}, model_dir / 'scalers.pt')
        
        # Save config
        config = {
            'model_type': 'pytorch_lstm_best',
            'timeframe': timeframe,
            'features': 5,
            'feature_columns': ['open', 'high', 'low', 'close', 'real_volume'],
            'hidden_dim': 64,
            'num_layers': 2,
            'dropout_rate': 0.2,
            'learning_rate': 0.001,
            'batch_size': 32,
            'sequence_length': 60,
            'epochs': 150,
            'patience': 15,
            'r2': float(r2),
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'timestamp': datetime.now().isoformat(),
            'notes': 'Proven working configuration with extended training'
        }
        
        with open(model_dir / 'config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"✅ Model saved to {model_dir}")
    
    return {
        'r2': r2,
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'status': status,
        'model': model,
        'scalers': {'X_scaler': X_scaler, 'y_scaler': y_scaler}
    }

def main():
    """Main function."""
    os.makedirs('logs', exist_ok=True)
    
    result = train_best_lstm('M5')
    
    if result and result['r2'] > 0.95:
        logger.info("🎉 SUCCESS: LSTM achieved excellent performance!")
        return 0
    elif result and result['r2'] > 0.88:
        logger.info("✅ GOOD: LSTM maintained/improved good performance!")
        return 0
    else:
        logger.info("❌ ISSUE: LSTM performance below expectations")
        return 1

if __name__ == "__main__":
    sys.exit(main())
