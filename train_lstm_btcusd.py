#!/usr/bin/env python
"""
LSTM Model Training Script for BTCUSD.a

This script trains LSTM models on BTCUSD.a data for multiple timeframes
using PyTorch with GPU acceleration.
"""

import os
import logging
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
# Removed train_test_split import - using proper temporal splitting instead
from pathlib import Path
import json
from datetime import datetime

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/train_lstm_btcusd.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import required modules
from models.pytorch_lstm_model import LSTMModel, LSTMTrainer
from utils.torch_gpu_config import get_gpu_info, select_device, configure_gpu_memory

def load_data(timeframe, data_dir='data/historical/btcusd.a'):
    """Load data for a specific timeframe."""
    try:
        file_path = Path(data_dir) / f"BTCUSD.a_{timeframe}.parquet"
        df = pd.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {timeframe} from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading data for {timeframe}: {str(e)}")
        return None

def add_advanced_features(df):
    """Add advanced feature engineering similar to ARIMA ensemble approach."""
    logger.info("Adding advanced feature engineering...")

    # Ensure we have a time index
    if 'time' not in df.columns and df.index.name != 'time':
        if hasattr(df.index, 'to_pydatetime'):
            df = df.reset_index()
            df['time'] = pd.to_datetime(df['time'])
        else:
            logger.warning("No time index found, using sequential index")
            df['time'] = pd.date_range(start='2019-01-01', periods=len(df), freq='5T')

    features_df = df.copy()

    # Rolling statistics (5 windows × 5 statistics = 25 features)
    windows = [5, 10, 20, 50, 100]
    for window in windows:
        features_df[f'sma_{window}'] = df['close'].rolling(window).mean()
        features_df[f'std_{window}'] = df['close'].rolling(window).std()
        features_df[f'min_{window}'] = df['close'].rolling(window).min()
        features_df[f'max_{window}'] = df['close'].rolling(window).max()
        features_df[f'q75_{window}'] = df['close'].rolling(window).quantile(0.75)

    # Lag features (6 lags = 6 features)
    lags = [1, 2, 3, 5, 10, 20]
    for lag in lags:
        features_df[f'lag_{lag}'] = df['close'].shift(lag)

    # Temporal features (8 features)
    if 'time' in features_df.columns:
        time_col = pd.to_datetime(features_df['time'])
        features_df['hour'] = time_col.dt.hour
        features_df['day_of_week'] = time_col.dt.dayofweek
        features_df['month'] = time_col.dt.month
        features_df['quarter'] = time_col.dt.quarter

        # Cyclical encoding (4 features)
        features_df['hour_sin'] = np.sin(2 * np.pi * features_df['hour'] / 24)
        features_df['hour_cos'] = np.cos(2 * np.pi * features_df['hour'] / 24)
        features_df['dow_sin'] = np.sin(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['dow_cos'] = np.cos(2 * np.pi * features_df['day_of_week'] / 7)

    # Volatility measures (3 features)
    for period in [5, 10, 20]:
        features_df[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std()

    # Momentum indicators (3 features)
    for period in [5, 10, 20]:
        features_df[f'momentum_{period}'] = df['close'].pct_change(period)

    # Price ratios (4 features)
    features_df['hl_ratio'] = df['high'] / df['low']
    features_df['oc_ratio'] = df['open'] / df['close']
    features_df['hc_ratio'] = df['high'] / df['close']
    features_df['lc_ratio'] = df['low'] / df['close']

    # Volume features (3 features)
    if 'real_volume' in df.columns:
        features_df['volume_sma_10'] = df['real_volume'].rolling(10).mean()
        features_df['volume_ratio'] = df['real_volume'] / (features_df['volume_sma_10'] + 1e-8)
        features_df['price_volume'] = df['close'] * df['real_volume']

    # Technical indicators (20+ features)
    # RSI for multiple periods
    for rsi_period in [14, 21, 30]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(rsi_period).mean()
        rs = gain / (loss + 1e-8)
        features_df[f'rsi_{rsi_period}'] = 100 - (100 / (1 + rs))

    # MACD variations
    for fast, slow in [(12, 26), (8, 21), (5, 13)]:
        ema_fast = df['close'].ewm(span=fast).mean()
        ema_slow = df['close'].ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        features_df[f'macd_{fast}_{slow}'] = macd
        features_df[f'macd_signal_{fast}_{slow}'] = macd.ewm(span=9).mean()
        features_df[f'macd_hist_{fast}_{slow}'] = macd - features_df[f'macd_signal_{fast}_{slow}']

    # Bollinger Bands for multiple periods
    for bb_period in [20, 50]:
        bb_std = 2
        bb_middle = df['close'].rolling(bb_period).mean()
        bb_std_dev = df['close'].rolling(bb_period).std()
        features_df[f'bb_upper_{bb_period}'] = bb_middle + (bb_std_dev * bb_std)
        features_df[f'bb_lower_{bb_period}'] = bb_middle - (bb_std_dev * bb_std)
        features_df[f'bb_width_{bb_period}'] = features_df[f'bb_upper_{bb_period}'] - features_df[f'bb_lower_{bb_period}']
        features_df[f'bb_position_{bb_period}'] = (df['close'] - features_df[f'bb_lower_{bb_period}']) / (features_df[f'bb_upper_{bb_period}'] - features_df[f'bb_lower_{bb_period}'])

    # Additional technical indicators
    # Stochastic Oscillator
    for stoch_period in [14, 21]:
        low_min = df['low'].rolling(stoch_period).min()
        high_max = df['high'].rolling(stoch_period).max()
        features_df[f'stoch_k_{stoch_period}'] = 100 * (df['close'] - low_min) / (high_max - low_min + 1e-8)
        features_df[f'stoch_d_{stoch_period}'] = features_df[f'stoch_k_{stoch_period}'].rolling(3).mean()

    # Williams %R
    for wr_period in [14, 21]:
        high_max = df['high'].rolling(wr_period).max()
        low_min = df['low'].rolling(wr_period).min()
        features_df[f'williams_r_{wr_period}'] = -100 * (high_max - df['close']) / (high_max - low_min + 1e-8)

    # Commodity Channel Index (CCI)
    for cci_period in [14, 20]:
        tp = (df['high'] + df['low'] + df['close']) / 3
        sma_tp = tp.rolling(cci_period).mean()
        mad = tp.rolling(cci_period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        features_df[f'cci_{cci_period}'] = (tp - sma_tp) / (0.015 * mad + 1e-8)

    # Remove time column and original OHLCV columns to avoid duplication
    features_df = features_df.drop(['time'], axis=1, errors='ignore')

    # Remove NaN values
    initial_rows = len(features_df)
    features_df = features_df.dropna()
    final_rows = len(features_df)

    logger.info(f"Created {len(features_df.columns)} features")
    logger.info(f"Removed {initial_rows - final_rows} rows with NaN values")
    logger.info(f"Final dataset: {final_rows} rows")

    return features_df

def preprocess_data(df, sequence_length=60, target_column='close',
                   feature_columns=None, test_size=0.2, use_advanced_features=True):
    """Preprocess data for LSTM training with advanced feature engineering."""

    # Validate input data
    if df is None or df.empty:
        raise ValueError("Input dataframe is None or empty")

    # Add advanced features if requested
    if use_advanced_features:
        logger.info("Using advanced feature engineering (85+ features)")
        df_features = add_advanced_features(df)

        # Use all engineered features except the target
        feature_columns = [col for col in df_features.columns if col != target_column]
        logger.info(f"Using {len(feature_columns)} engineered features")
    else:
        # Use basic features if specified
        if feature_columns is None:
            feature_columns = ['open', 'high', 'low', 'close', 'real_volume']
        df_features = df.copy()

    # Check if required columns exist
    missing_cols = [col for col in feature_columns + [target_column] if col not in df_features.columns]
    if missing_cols:
        raise ValueError(f"Missing columns in dataframe: {missing_cols}")

    # Check for sufficient data
    if len(df_features) < sequence_length + 1:
        raise ValueError(f"Insufficient data: need at least {sequence_length + 1} rows, got {len(df_features)}")

    # Extract features and target
    X = df_features[feature_columns].values
    y = df_features[target_column].values.reshape(-1, 1)

    # Scale features and target separately
    X_scaler = StandardScaler()
    y_scaler = StandardScaler()

    X_scaled = X_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    # Create sequences more efficiently
    X_sequences = []
    y_sequences = []

    for i in range(len(X_scaled) - sequence_length):
        X_sequences.append(X_scaled[i:i+sequence_length])
        y_sequences.append(y_scaled[i+sequence_length])

    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)

    logger.info(f"Created {len(X_sequences)} sequences from {len(df_features)} data points")
    logger.info(f"Feature shape: {X_sequences.shape}, Target shape: {y_sequences.shape}")

    # Split into train and test sets using proper temporal splitting
    split_idx = int(len(X_sequences) * (1 - test_size))
    X_train = X_sequences[:split_idx]
    X_test = X_sequences[split_idx:]
    y_train = y_sequences[:split_idx]
    y_test = y_sequences[split_idx:]

    return X_train, X_test, y_train, y_test, X_scaler, y_scaler

def train_lstm_model(timeframe, feature_columns=None, target_column='close',
                    sequence_length=60, hidden_units=256, num_layers=4,
                    dropout_rate=0.4, learning_rate=0.0001, epochs=200,
                    batch_size=128, validation_split=0.1, test_size=0.2,
                    random_state=42, use_gpu=True):
    """Train LSTM model for a specific timeframe."""
    try:
        # Configure GPU
        gpu_info = get_gpu_info()
        if gpu_info['gpu_available'] and use_gpu:
            logger.info(f"GPU is available: {gpu_info['gpu_devices']}")
            configure_gpu_memory()
            device = select_device(use_gpu=True)
            logger.info(f"Using device: {device}")
        else:
            if not gpu_info['gpu_available']:
                logger.warning("No GPU available, using CPU for training")
            elif not use_gpu:
                logger.warning("GPU available but use_gpu=False, using CPU for training")
            device = torch.device('cpu')

        # Load data
        df = load_data(timeframe)
        if df is None:
            logger.error(f"Failed to load data for timeframe {timeframe}")
            return None

        # Preprocess data with advanced feature engineering
        try:
            X_train, X_test, y_train, y_test, X_scaler, y_scaler = preprocess_data(
                df, sequence_length, target_column, feature_columns, test_size, use_advanced_features=True
            )
        except ValueError as e:
            logger.error(f"Data preprocessing failed for {timeframe}: {str(e)}")
            return None

        # Convert data to PyTorch tensors
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train)
        X_test_tensor = torch.FloatTensor(X_test)
        y_test_tensor = torch.FloatTensor(y_test)

        # Create datasets and data loaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        # Split training data for validation
        train_size = int((1 - validation_split) * len(train_dataset))
        val_size = len(train_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            train_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(random_state)
        )

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size)
        test_loader = DataLoader(test_dataset, batch_size=batch_size)

        # Get input shape
        _, timesteps, features = X_train.shape

        # Create model
        model = LSTMModel(
            input_dim=features,
            hidden_dim=hidden_units,
            num_layers=num_layers,
            output_dim=1,
            dropout_rate=dropout_rate
        )

        # Create trainer
        trainer = LSTMTrainer(
            model=model,
            learning_rate=learning_rate,
            device=device
        )

        # Train model
        logger.info(f"Training PyTorch LSTM model for BTCUSD.a {timeframe} on {device}")
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=epochs,
            patience=10,
            verbose=True
        )

        # Evaluate model
        test_loss = trainer.evaluate(test_loader)
        logger.info(f"Test loss: {test_loss}")

        # Make predictions
        y_pred = trainer.predict(test_loader)

        # Inverse transform predictions and actual values
        y_pred_inv = y_scaler.inverse_transform(y_pred)
        y_test_inv = y_scaler.inverse_transform(y_test)

        # Calculate metrics
        mse = np.mean((y_pred_inv - y_test_inv) ** 2)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_pred_inv - y_test_inv))

        # Calculate R² (coefficient of determination)
        y_mean = np.mean(y_test_inv)
        ss_total = np.sum((y_test_inv - y_mean) ** 2)
        ss_residual = np.sum((y_test_inv - y_pred_inv) ** 2)
        r2 = 1 - (ss_residual / ss_total)

        logger.info(f"MSE: {mse}, RMSE: {rmse}, MAE: {mae}, R2: {r2}")

        # Save model with error handling
        try:
            model_name = f"lstm_BTCUSD.a_{timeframe}"
            model_dir = Path("models") / model_name
            model_dir.mkdir(parents=True, exist_ok=True)

            # Save model
            model.save(str(model_dir))
            logger.info(f"Model weights saved to {model_dir}")

            # Save scalers
            scalers = {'X_scaler': X_scaler, 'y_scaler': y_scaler}
            torch.save(scalers, model_dir / "scalers.pt")
            logger.info(f"Scalers saved to {model_dir / 'scalers.pt'}")

            # Save configuration
            config = {
                'model_type': 'pytorch_lstm',
                'symbol': 'BTCUSD.a',
                'timeframe': timeframe,
                'sequence_length': sequence_length,
                'feature_columns': feature_columns,
                'target_column': target_column,
                'hidden_units': hidden_units,
                'num_layers': num_layers,
                'dropout_rate': dropout_rate,
                'learning_rate': learning_rate,
                'input_shape': (None, timesteps, features),
                'device': str(device),
                'gpu_available': gpu_info['gpu_available'],
                'gpu_used': gpu_info['gpu_available'] and use_gpu,
                'pytorch_version': torch.__version__,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'epochs_trained': len(history['train_loss']),
                'metrics': {
                    'mse': float(mse),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'r2': float(r2)
                }
            }

            # Save configuration as JSON
            with open(model_dir / "config.json", "w") as f:
                json.dump(config, f, indent=4)

            logger.info(f"Configuration saved to {model_dir / 'config.json'}")
            logger.info(f"Model successfully saved to {model_dir}")

        except Exception as e:
            logger.error(f"Error saving model for {timeframe}: {str(e)}")
            # Continue execution even if saving fails

        return {
            'model': model,
            'history': history,
            'metrics': {
                'mse': float(mse),
                'rmse': float(rmse),
                'mae': float(mae),
                'r2': float(r2)
            },
            'config': config
        }

    except Exception as e:
        logger.error(f"Error training LSTM model for {timeframe}: {str(e)}", exc_info=True)
        return None

def main():
    """Main function to train LSTM models for all timeframes."""
    # Define timeframes
    timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']

    # Define feature columns
    feature_columns = ['open', 'high', 'low', 'close', 'real_volume']

    # Store metrics for all timeframes
    all_metrics = {}

    # Train models for all timeframes
    for timeframe in timeframes:
        logger.info(f"Processing timeframe: {timeframe}")

        result = train_lstm_model(
            timeframe=timeframe,
            feature_columns=feature_columns,
            target_column='close',
            sequence_length=60,
            hidden_units=256,
            num_layers=4,
            dropout_rate=0.4,
            learning_rate=0.0001,
            epochs=200,
            batch_size=128,
            validation_split=0.1,
            test_size=0.2,
            random_state=42,
            use_gpu=True
        )

        if result:
            logger.info(f"Successfully trained LSTM model for BTCUSD.a {timeframe}")
            logger.info(f"Metrics: MSE={result['metrics']['mse']:.6f}, RMSE={result['metrics']['rmse']:.6f}, MAE={result['metrics']['mae']:.6f}, R2={result['metrics'].get('r2', 0.0):.6f}")

            # Store metrics for this timeframe
            all_metrics[timeframe] = result['metrics']
        else:
            logger.error(f"Failed to train LSTM model for BTCUSD.a {timeframe}")

    # Save all metrics to a summary file
    os.makedirs('metrics', exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    summary_file = f"metrics/lstm_BTCUSD.a_summary_{timestamp}.json"

    with open(summary_file, 'w') as f:
        json.dump({
            'model_type': 'lstm',
            'symbol': 'BTCUSD.a',
            'timeframes': timeframes,
            'metrics': all_metrics
        }, f, indent=4)

    logger.info(f"Metrics summary saved to {summary_file}")

    # Print comparison table
    logger.info("Metrics comparison across timeframes:")
    logger.info(f"{'Timeframe':<10} {'MSE':<15} {'RMSE':<15} {'MAE':<15} {'R2':<15}")
    logger.info("-" * 70)

    for tf in timeframes:
        if tf in all_metrics:
            metrics = all_metrics[tf]
            r2_value = metrics.get('r2', 0.0)  # Use 0.0 as default if r2 is not present
            logger.info(f"{tf:<10} {metrics['mse']:<15.6f} {metrics['rmse']:<15.6f} {metrics['mae']:<15.6f} {r2_value:<15.6f}")
        else:
            logger.info(f"{tf:<10} {'Failed':<15} {'Failed':<15} {'Failed':<15} {'Failed':<15}")

if __name__ == "__main__":
    main()
