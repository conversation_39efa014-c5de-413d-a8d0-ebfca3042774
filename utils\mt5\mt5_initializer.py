"""
Standardized MT5 Initialization Utility

This module provides a consistent way to initialize MT5 connections across the entire codebase.
It ensures that algorithmic trading is preserved and follows best practices for MT5 initialization.

Key principles:
1. Always use portable=True to preserve algorithmic trading settings
2. Minimal initialization when possible to avoid disrupting other terminals
3. Proper error handling and logging
4. Consistent terminal ID handling (always strings)
"""

import logging
import MetaTrader5 as mt5
from typing import Optional, Union, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class MT5InitializationError(Exception):
    """Custom exception for MT5 initialization errors."""
    pass

class MT5Initializer:
    """
    Standardized MT5 initialization utility.
    
    This class provides consistent MT5 initialization patterns across the codebase
    while preserving algorithmic trading functionality.
    """
    
    @staticmethod
    def minimal_init() -> bool:
        """
        Perform minimal MT5 initialization to preserve algorithmic trading.

        This should be used when you just need to check if MT5 is available
        or perform basic operations without disrupting existing connections.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # First check if MT5 is already initialized
            if mt5.terminal_info():
                logger.debug("MT5 already initialized - avoiding reinitialization to preserve Algo Trading")
                return True

            # Only initialize if MT5 is not connected at all
            # Try minimal initialization with portable=True
            if mt5.initialize(portable=True):
                logger.debug("MT5 minimal initialization successful")
                return True
            else:
                error = mt5.last_error()
                logger.warning(f"MT5 minimal initialization failed: {error}")
                return False

        except Exception as e:
            logger.error(f"Error during MT5 minimal initialization: {str(e)}")
            return False
    
    @staticmethod
    def full_init(terminal_config: Dict[str, Any]) -> bool:
        """
        Perform full MT5 initialization with terminal credentials.
        
        This should be used when you need to connect to a specific terminal
        with full authentication.
        
        Args:
            terminal_config: Dictionary containing terminal configuration
                           Must include: path, login, password, server
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # First check if MT5 is already initialized and connected to the right account
            terminal_info = mt5.terminal_info()
            if terminal_info:
                account_info = mt5.account_info()
                if account_info:
                    # Convert login to int if it's a string for comparison
                    target_login = terminal_config['login']
                    if isinstance(target_login, str) and target_login.isdigit():
                        target_login = int(target_login)

                    if account_info.login == target_login:
                        logger.info(f"MT5 already connected to target account {target_login} - avoiding reinitialization")
                        return True

            # Validate terminal config
            required_keys = ['path', 'login', 'password', 'server']
            for key in required_keys:
                if key not in terminal_config:
                    raise MT5InitializationError(f"Missing required config key: {key}")

            # Ensure path exists
            path = Path(terminal_config['path'])
            if not path.exists():
                raise MT5InitializationError(f"Terminal path not found: {path}")

            # Convert login to int if it's a string
            login = terminal_config['login']
            if isinstance(login, str) and login.isdigit():
                login = int(login)

            # Initialize MT5 with full credentials only if not already connected
            success = mt5.initialize(
                path=str(path),
                login=login,
                password=terminal_config['password'],
                server=terminal_config['server'],
                portable=True  # CRITICAL: Must be True to preserve algorithmic trading
            )
            
            if success:
                logger.info(f"MT5 full initialization successful for {terminal_config.get('server', 'unknown server')}")
                return True
            else:
                error = mt5.last_error()
                logger.error(f"MT5 full initialization failed: {error}")
                return False
                
        except Exception as e:
            logger.error(f"Error during MT5 full initialization: {str(e)}")
            return False
    
    @staticmethod
    def safe_reinit() -> bool:
        """
        Safely reinitialize MT5 to preserve algorithmic trading settings.

        This should be used when switching between terminals or recovering
        from connection issues.

        Returns:
            bool: True if reinitialization successful, False otherwise
        """
        try:
            logger.info("Performing safe MT5 reinitialization")

            # Check if MT5 is already connected - if so, DO NOT reinitialize
            if mt5.terminal_info():
                logger.info("MT5 already connected - skipping reinitialization to preserve Algo Trading")
                return True

            # Only reinitialize if MT5 is not connected
            # DO NOT call mt5.shutdown() as it disables algorithmic trading
            # Instead, reinitialize with portable=True to preserve settings
            if mt5.initialize(portable=True):
                logger.info("MT5 safe reinitialization successful")
                return True
            else:
                error = mt5.last_error()
                logger.warning(f"MT5 safe reinitialization failed: {error}")
                return False
                
        except Exception as e:
            logger.error(f"Error during MT5 safe reinitialization: {str(e)}")
            return False
    
    @staticmethod
    def check_connection() -> bool:
        """
        Check if MT5 is properly connected and responsive.
        
        Returns:
            bool: True if MT5 is connected and responsive, False otherwise
        """
        try:
            # Try to get terminal info
            terminal_info = mt5.terminal_info()
            if terminal_info:
                logger.debug("MT5 connection check successful")
                return True
            else:
                logger.warning("MT5 connection check failed - no terminal info")
                return False
                
        except Exception as e:
            logger.error(f"Error during MT5 connection check: {str(e)}")
            return False
    
    @staticmethod
    def check_algo_trading() -> bool:
        """
        Check if algorithmic trading is enabled.
        
        Returns:
            bool: True if algorithmic trading is enabled, False otherwise
        """
        try:
            terminal_info = mt5.terminal_info()
            if not terminal_info:
                logger.warning("Cannot check algo trading - no terminal info")
                return False
            
            # Check if algorithmic trading is enabled
            if terminal_info.trade_allowed:
                logger.debug("Algorithmic trading is enabled")
                return True
            else:
                logger.warning("Algorithmic trading is DISABLED")
                logger.warning("Please enable algorithmic trading in the MT5 terminal:")
                logger.warning("1. Click the 'Algo Trading' button in the toolbar")
                logger.warning("2. Verify that 'Algo Trading enabled' appears in the status bar")
                return False
                
        except Exception as e:
            logger.error(f"Error checking algorithmic trading status: {str(e)}")
            return False
    
    @staticmethod
    def get_initialization_info() -> Optional[Dict[str, Any]]:
        """
        Get information about the current MT5 initialization.
        
        Returns:
            Dict with initialization info or None if not initialized
        """
        try:
            terminal_info = mt5.terminal_info()
            if not terminal_info:
                return None
            
            account_info = mt5.account_info()
            
            return {
                'terminal_path': terminal_info.path,
                'terminal_name': terminal_info.name,
                'terminal_company': terminal_info.company,
                'trade_allowed': terminal_info.trade_allowed,
                'account_login': account_info.login if account_info else None,
                'account_server': account_info.server if account_info else None,
                'connected': True
            }
            
        except Exception as e:
            logger.error(f"Error getting MT5 initialization info: {str(e)}")
            return None

# Convenience functions for common operations
def ensure_mt5_initialized() -> bool:
    """
    Ensure MT5 is initialized with minimal disruption.
    
    Returns:
        bool: True if MT5 is initialized, False otherwise
    """
    return MT5Initializer.minimal_init()

def safe_mt5_reconnect() -> bool:
    """
    Safely reconnect to MT5 preserving algorithmic trading.
    
    Returns:
        bool: True if reconnection successful, False otherwise
    """
    return MT5Initializer.safe_reinit()

def check_mt5_algo_trading() -> bool:
    """
    Check if algorithmic trading is enabled.
    
    Returns:
        bool: True if algorithmic trading is enabled, False otherwise
    """
    return MT5Initializer.check_algo_trading()
