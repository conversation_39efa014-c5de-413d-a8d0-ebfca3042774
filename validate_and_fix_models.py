#!/usr/bin/env python
"""
Model Validation and Fix Script

This script validates all models and fixes any issues found to restore
the documented R² performance levels.
"""

import os
import sys
import json
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/model_validation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelValidator:
    """Validates and fixes model training issues."""
    
    def __init__(self):
        self.timeframes = ['M5', 'M15', 'M30', 'H1', 'H4']
        self.models_dir = Path('models')
        self.metrics_dir = Path('metrics')
        self.data_dir = Path('data/historical/btcusd.a')
        
        # Expected R² values from documentation
        self.expected_r2 = {
            'lstm': 0.9997,  # From documentation
            'arima': 0.9784,  # From ensemble ARIMA analysis
            'tft': 0.529     # From TFT analysis (improved)
        }
        
        # Minimum acceptable R² values
        self.min_r2 = {
            'lstm': 0.95,
            'arima': 0.90,
            'tft': 0.50
        }
    
    def validate_data_availability(self) -> Dict[str, bool]:
        """Validate that all required data files exist."""
        logger.info("Validating data availability...")
        
        data_status = {}
        for timeframe in self.timeframes:
            data_file = self.data_dir / f"BTCUSD.a_{timeframe}.parquet"
            data_status[timeframe] = data_file.exists()
            
            if data_status[timeframe]:
                try:
                    df = pd.read_parquet(data_file)
                    logger.info(f"✅ {timeframe}: {len(df)} rows available")
                except Exception as e:
                    logger.error(f"❌ {timeframe}: Error reading data - {str(e)}")
                    data_status[timeframe] = False
            else:
                logger.error(f"❌ {timeframe}: Data file not found")
        
        return data_status
    
    def validate_model_performance(self) -> Dict[str, Dict[str, float]]:
        """Validate current model performance against expected values."""
        logger.info("Validating model performance...")
        
        performance = {}
        
        # Get latest metrics for each model type
        for model_type in ['lstm', 'arima', 'tft']:
            performance[model_type] = {}
            
            # Find latest metrics file for this model type
            pattern = f"{model_type}_BTCUSD.a_*_*.json"
            metrics_files = list(self.metrics_dir.glob(pattern))
            
            if not metrics_files:
                logger.warning(f"No metrics files found for {model_type}")
                continue
            
            # Get the most recent file
            latest_file = max(metrics_files, key=lambda x: x.stat().st_mtime)
            
            try:
                with open(latest_file, 'r') as f:
                    metrics = json.load(f)
                
                if 'metrics' in metrics:
                    r2 = metrics['metrics'].get('r2', 0.0)
                else:
                    r2 = metrics.get('r2', 0.0)
                
                performance[model_type]['current_r2'] = r2
                performance[model_type]['expected_r2'] = self.expected_r2[model_type]
                performance[model_type]['meets_minimum'] = r2 >= self.min_r2[model_type]
                performance[model_type]['file'] = str(latest_file)
                
                status = "✅" if performance[model_type]['meets_minimum'] else "❌"
                logger.info(f"{status} {model_type.upper()}: R² = {r2:.4f} (expected: {self.expected_r2[model_type]:.4f})")
                
            except Exception as e:
                logger.error(f"Error reading metrics for {model_type}: {str(e)}")
        
        return performance
    
    def check_model_files(self) -> Dict[str, Dict[str, bool]]:
        """Check if model files exist for all timeframes."""
        logger.info("Checking model file availability...")
        
        model_status = {}
        
        for model_type in ['lstm', 'arima', 'tft']:
            model_status[model_type] = {}
            
            for timeframe in self.timeframes:
                model_dir = self.models_dir / f"{model_type}_BTCUSD.a_{timeframe}"
                
                if model_type == 'lstm':
                    required_files = ['model.pt', 'scalers.pt', 'config.json']
                elif model_type == 'arima':
                    required_files = ['model.pkl', 'config.json']
                elif model_type == 'tft':
                    required_files = ['model.pt', 'scalers.pt']
                
                all_exist = model_dir.exists() and all(
                    (model_dir / file).exists() for file in required_files
                )
                
                model_status[model_type][timeframe] = all_exist
                status = "✅" if all_exist else "❌"
                logger.info(f"{status} {model_type.upper()} {timeframe}: Model files")
        
        return model_status
    
    def identify_training_issues(self) -> List[str]:
        """Identify specific training issues that need to be fixed."""
        logger.info("Identifying training issues...")
        
        issues = []
        
        # Check data availability
        data_status = self.validate_data_availability()
        missing_data = [tf for tf, exists in data_status.items() if not exists]
        if missing_data:
            issues.append(f"Missing data for timeframes: {missing_data}")
        
        # Check model performance
        performance = self.validate_model_performance()
        for model_type, perf in performance.items():
            if not perf.get('meets_minimum', False):
                current = perf.get('current_r2', 0.0)
                expected = perf.get('expected_r2', 0.0)
                issues.append(f"{model_type.upper()} underperforming: R²={current:.4f} (expected: {expected:.4f})")
        
        # Check model files
        model_status = self.check_model_files()
        for model_type, timeframes in model_status.items():
            missing_timeframes = [tf for tf, exists in timeframes.items() if not exists]
            if missing_timeframes:
                issues.append(f"Missing {model_type.upper()} models for: {missing_timeframes}")
        
        return issues
    
    def generate_fix_commands(self, issues: List[str]) -> List[str]:
        """Generate specific commands to fix identified issues."""
        logger.info("Generating fix commands...")
        
        commands = []
        
        for issue in issues:
            if "ARIMA underperforming" in issue:
                commands.append("python train_arima_single.py --timeframe M5 --target-column close --auto-arima --max-rows 50000 --data-selection all --use-ensemble --ensemble-models 5")
                commands.append("train_all_arima_models.bat")
            
            elif "LSTM underperforming" in issue:
                commands.append("python train_lstm_btcusd.py")
                commands.append("train_all_lstm_models.bat")
            
            elif "TFT underperforming" in issue:
                commands.append("python train_tft_pytorch.py --timeframe M5 --hidden-dim 64 --num-heads 4 --num-layers 2 --dropout-rate 0.1 --learning-rate 0.001 --epochs 5 --batch-size 32")
                commands.append("train_all_tft_models.bat")
            
            elif "Missing" in issue and "models" in issue:
                if "ARIMA" in issue:
                    commands.append("train_all_arima_models.bat")
                elif "LSTM" in issue:
                    commands.append("train_all_lstm_models.bat")
                elif "TFT" in issue:
                    commands.append("train_all_tft_models.bat")
        
        return list(set(commands))  # Remove duplicates
    
    def run_validation(self) -> Dict:
        """Run complete validation and return results."""
        logger.info("="*60)
        logger.info("STARTING COMPREHENSIVE MODEL VALIDATION")
        logger.info("="*60)
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'data_status': self.validate_data_availability(),
            'performance': self.validate_model_performance(),
            'model_files': self.check_model_files(),
            'issues': self.identify_training_issues()
        }
        
        results['fix_commands'] = self.generate_fix_commands(results['issues'])
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("VALIDATION SUMMARY")
        logger.info("="*60)
        
        if not results['issues']:
            logger.info("🎉 ALL MODELS ARE PERFORMING OPTIMALLY!")
        else:
            logger.info(f"⚠️  FOUND {len(results['issues'])} ISSUES:")
            for i, issue in enumerate(results['issues'], 1):
                logger.info(f"   {i}. {issue}")
            
            logger.info(f"\n📋 RECOMMENDED FIX COMMANDS:")
            for i, cmd in enumerate(results['fix_commands'], 1):
                logger.info(f"   {i}. {cmd}")
        
        # Save results
        results_file = self.metrics_dir / f"validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\n📄 Results saved to: {results_file}")
        
        return results

def main():
    """Main function."""
    # Create necessary directories
    Path('logs').mkdir(exist_ok=True)
    Path('metrics').mkdir(exist_ok=True)
    
    # Run validation
    validator = ModelValidator()
    results = validator.run_validation()
    
    return 0 if not results['issues'] else 1

if __name__ == "__main__":
    sys.exit(main())
